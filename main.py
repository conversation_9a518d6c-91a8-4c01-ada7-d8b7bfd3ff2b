from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket, Query
import json
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import time
import uuid

from config import settings
from database import create_db_and_tables
from utils.logger import setup_logging, get_logger, get_log_info
from services.webrtc_redis_service import get_redis_service

# Import routers
from routers import auth, device, llm, shop, staff, admin, webrtc, order

# Setup logging
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Robot Control API")

    # 显示日志配置信息
    log_info = get_log_info()
    logger.info(
        f"日志配置: 级别={log_info['log_level']}, 轮转={log_info['rotation_when']}, 保留={log_info['backup_count']}天")
    logger.info(f"当前日志: {log_info['total_files']}个文件, 总大小={log_info['total_size_mb']}MB")

    # Create database tables
    create_db_and_tables()
    logger.info("Database tables created/verified")

    # 启动Redis消息中继
    redis_service = get_redis_service()
    await redis_service.start_message_relay()
    logger.info("Redis message relay started")

    yield

    # Shutdown
    logger.info("Shutting down Robot Control API")

    # 停止Redis消息中继
    redis_service = get_redis_service()
    await redis_service.stop_message_relay()
    logger.info("Redis message relay stopped")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Robot Control API for Orange Pi and WebRTC integration",
    debug=settings.debug,
    lifespan=lifespan
)

# Add CORS middleware with comprehensive configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=False,  # 当allow_origins为["*"]时，必须设置为False
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers",
    ],
    expose_headers=["*"],
)


# Manual CORS handling for problematic requests
@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    """Add CORS headers manually for better compatibility"""

    # Handle preflight requests
    if request.method == "OPTIONS":
        response = JSONResponse(content={})
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH"
        response.headers[
            "Access-Control-Allow-Headers"] = "Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, Origin, Access-Control-Request-Method, Access-Control-Request-Headers"
        response.headers["Access-Control-Max-Age"] = "86400"
        return response

    # Process normal requests
    response = await call_next(request)

    # Add CORS headers to response
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH"
    response.headers[
        "Access-Control-Allow-Headers"] = "Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, Origin, Access-Control-Request-Method, Access-Control-Request-Headers"

    return response


# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests"""
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Log request
    logger.info(
        "Request started",
        extra={
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host if request.client else None
        }
    )

    # Process request
    response = await call_next(request)

    # Log response
    process_time = time.time() - start_time
    logger.info(
        "Request completed",
        extra={
            "request_id": request_id,
            "status_code": response.status_code,
            "process_time": process_time
        }
    )

    return response


# Include routers
app.include_router(auth.router)
app.include_router(device.router)
app.include_router(llm.router)
app.include_router(order.router)
app.include_router(webrtc.router)  # WebRTC路由 (生产系统)
app.include_router(shop.router)
app.include_router(staff.router)
app.include_router(admin.router)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Robot Control API",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/ping")
async def test_ping():
    """简单的ping测试"""
    return {"message": "pong", "status": "ok", "timestamp": time.time()}


# 简单的WebSocket测试
test_connections = {}


@app.websocket("/test-ws")
async def test_websocket(websocket: WebSocket, client_id: str = Query(...), client_type: str = Query("user")):
    await websocket.accept()

    test_connections[client_id] = {
        "websocket": websocket,
        "client_type": client_type
    }

    print(f"[TEST] {client_type} {client_id} connected")

    try:
        await websocket.send_text(json.dumps({
            "type": "connected",
            "client_id": client_id,
            "client_type": client_type,
            "message": "测试连接成功"
        }))

        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            print(f"[TEST] Received from {client_id}: {message.get('type')}")

            # 中继消息给其他客户端
            await relay_test_message(client_id, message)

    except Exception as e:
        print(f"[TEST] WebSocket error for {client_id}: {e}")
    finally:
        if client_id in test_connections:
            del test_connections[client_id]
        print(f"[TEST] {client_id} disconnected")


async def relay_test_message(sender_id: str, message: dict):
    """中继测试消息给其他客户端"""
    sender_info = test_connections.get(sender_id)
    if not sender_info:
        return

    sender_type = sender_info["client_type"]

    # 查找目标客户端（不同类型的客户端）
    for client_id, conn_info in test_connections.items():
        if client_id != sender_id and conn_info["client_type"] != sender_type:
            try:
                # 添加发送者信息
                relay_message = {
                    "type": f"relay_{message.get('type', 'message')}",
                    "from": sender_id,
                    "from_type": sender_type,
                    "original_message": message,
                    "timestamp": time.time()
                }

                await conn_info["websocket"].send_text(json.dumps(relay_message))
                print(f"[TEST] Relayed {message.get('type')} from {sender_id} to {client_id}")

            except Exception as e:
                print(f"[TEST] Failed to relay to {client_id}: {e}")

    # 也发送确认给发送者
    try:
        confirm_message = {
            "type": "sent_confirmation",
            "original_type": message.get('type'),
            "timestamp": time.time()
        }
        await sender_info["websocket"].send_text(json.dumps(confirm_message))
    except Exception as e:
        print(f"[TEST] Failed to send confirmation to {sender_id}: {e}")


@app.get("/test-status")
async def test_status():
    """获取测试状态"""
    return {
        "active_connections": len(test_connections),
        "connections": list(test_connections.keys())
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time()
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(
        f"Unhandled exception: {str(exc)}",
        extra={
            "url": str(request.url),
            "method": request.method,
            "exception_type": type(exc).__name__
        },
        exc_info=True
    )

    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )

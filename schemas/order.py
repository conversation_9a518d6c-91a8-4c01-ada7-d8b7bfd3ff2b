from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime


class OrderItemResponse(BaseModel):
    product_id: str
    product_name: str
    quantity: int
    price_at_purchase: float
    total_price: float


class OrderCreate(BaseModel):
    device_id: str
    items: List[dict]
    total_amount: float


class OrderResponse(BaseModel):
    id: str
    device_id: str
    total_amount: float
    status: str
    created_at: datetime
    items: List[OrderItemResponse] = []


class OrderStatusUpdate(BaseModel):
    status: str

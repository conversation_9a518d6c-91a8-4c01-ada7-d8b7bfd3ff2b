import os
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv('.env',verbose=True)

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://username:password@localhost:5432/robot_db"

    # JWT
    secret_key: str = "your-secret-key-here-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # Application
    app_name: str = "Robot Control API"
    app_version: str = "1.0.0"
    debug: bool = False

    # CORS - 临时使用宽松配置进行调试
    allowed_origins: List[str] = ["*"]

    # Logging Configuration
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    log_rotation_when: str = "midnight"    # 轮转时间: midnight, H, D, W0-W6
    log_rotation_interval: int = 1         # 轮转间隔
    log_backup_count: int = 30             # 保留日志文件数量（天数）
    log_max_bytes: int = 100 * 1024 * 1024 # 单个日志文件最大大小 (100MB)

    # Redis Configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    redis_max_connections: int = 20
    redis_retry_on_timeout: bool = True
    redis_socket_timeout: float = 5.0
    redis_socket_connect_timeout: float = 5.0
    redis_enabled: bool = True

    # WebRTC Redis Configuration
    webrtc_connection_ttl: int = 3600  # 1小时
    webrtc_session_ttl: int = 3600     # 1小时
    webrtc_cleanup_interval: int = 300  # 5分钟
    webrtc_key_prefix: str = "webrtc"

    @property
    def redis_url(self) -> str:
        """生成Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        else:
            return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    @property
    def redis_connection_config(self) -> dict:
        """生成Redis连接配置字典"""
        config = {
            "host": self.redis_host,
            "port": self.redis_port,
            "db": self.redis_db,
            "max_connections": self.redis_max_connections,
            "retry_on_timeout": self.redis_retry_on_timeout,
            "socket_timeout": self.redis_socket_timeout,
            "socket_connect_timeout": self.redis_socket_connect_timeout,
            "decode_responses": True
        }

        if self.redis_password:
            config["password"] = self.redis_password

        return config

    # Payment Configuration
    @property
    def payment_config(self) -> Dict[str, Dict[str, Any]]:
        """支付配置"""
        return {
            "alipay": {
                "server_url": os.getenv('ALIPAY_SERVER_URL', 'https://openapi-sandbox.dl.alipaydev.com/gateway.do'),
                "app_id": os.getenv('ALIPAY_APP_ID', ''),
                "app_private_key": os.getenv('ALIPAY_PRIVATE_KEY', ''),
                "alipay_public_key": os.getenv('ALIPAY_PUBLIC_KEY', ''),
                "notify_url": os.getenv('ALIPAY_NOTIFY_URL', 'http://localhost:8000/api/v1/shop/payment/callback/alipay'),
                "return_url": os.getenv('ALIPAY_RETURN_URL', 'http://localhost:8000/api/v1/shop/payment/return/alipay'),
            },
            "wechat": {
                # 微信支付配置（待实现）
                "app_id": os.getenv('WECHAT_APP_ID', ''),
                "mch_id": os.getenv('WECHAT_MCH_ID', ''),
                "api_key": os.getenv('WECHAT_API_KEY', ''),
                "notify_url": os.getenv('WECHAT_NOTIFY_URL', 'http://localhost:8000/api/v1/shop/payment/callback/wechat'),
            }
        }

    class Config:
        env_file = ".env"
        extra = "ignore"  # 忽略额外的环境变量


settings = Settings()


# 安全配置相关函数
def validate_password_strength(password: str) -> tuple[bool, list[str]]:
    """
    验证密码强度

    Args:
        password: 待验证的密码

    Returns:
        (是否通过验证, 错误信息列表)
    """
    errors = []

    # 检查长度
    if len(password) < 8:
        errors.append("密码长度至少需要8个字符")

    # 检查大写字母
    if not any(c.isupper() for c in password):
        errors.append("密码必须包含至少一个大写字母")

    # 检查小写字母
    if not any(c.islower() for c in password):
        errors.append("密码必须包含至少一个小写字母")

    # 检查数字
    if not any(c.isdigit() for c in password):
        errors.append("密码必须包含至少一个数字")

    # 检查常见弱密码
    weak_passwords = [
        "password", "123456", "123456789", "qwerty", "abc123",
        "password123", "admin", "root", "user", "guest"
    ]
    if password.lower() in weak_passwords:
        errors.append("不能使用常见的弱密码")

    return len(errors) == 0, errors


def generate_secure_key() -> str:
    """生成安全的密钥"""
    import secrets
    return secrets.token_urlsafe(32)


def is_secure_origin(origin: str) -> bool:
    """检查是否为安全的源"""
    # HTTPS协议
    if origin.startswith('https://'):
        return True

    # 本地开发环境
    local_patterns = ['localhost', '127.0.0.1', '192.168.', '10.']
    if any(pattern in origin for pattern in local_patterns):
        return os.getenv("ENVIRONMENT", "production") != "production"

    return False

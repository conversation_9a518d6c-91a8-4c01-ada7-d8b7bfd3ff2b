
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备支付测试 - 订单 d0625652-f3ce-4737-9030-86ecb8853f55</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .order-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .pay-button {
            display: block;
            width: 100%;
            padding: 15px;
            background-color: #1677ff;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
        }
        .pay-button:hover {
            background-color: #0958d9;
        }
        .alipay-button {
            background-color: #00a0e9;
        }
        .alipay-button:hover {
            background-color: #0087c1;
        }
        .info {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }
        .url-display {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 机器人设备支付测试</h1>
            <p>模拟设备端支付流程</p>
        </div>
        
        <div class="order-info">
            <h3>📋 订单信息</h3>
            <p><strong>订单号:</strong> d0625652-f3ce-4737-9030-86ecb8853f55</p>
            <p><strong>设备ID:</strong> test_device_payment_link</p>
            <p><strong>支付金额:</strong> ¥0.01</p>
            <p><strong>支付方式:</strong> 支付宝</p>
        </div>
        
        <a href="https://openapi-sandbox.dl.alipaydev.com/gateway.do?timestamp=2025-07-31+16%3A03%3A54&app_id=9021000138652386&method=alipay.trade.page.pay&charset=utf-8&format=json&version=1.0&sign_type=RSA2&notify_url=http%3A%2F%2Flocalhost%3A8000%2Fapi%2Fv1%2Fshop%2Fpayment%2Fcallback%2Falipay&return_url=http%3A%2F%2Flocalhost%3A8000%2Fpayment%2Fsuccess&sign=qPi9aVCbHan3QXblEOGRhZeRIfTYMYJOJtn5ZZ7OibQvoG5dUXcYsX0f5OLE%2BY8Fdy2mZIynMuo5UwE65HRT9waOhZ786DwPNayYLAb7mhBWUVP5KskSPgJvKuo9M99D%2FWwd%2BCgjURCqBgcEIO2pDyXbuPtjWylo7X2kCdsaZmEcidWxmXtCpTgjvDZrEynOrYHNPWf8V6wjOXIse6oEQN9qHohjs6fvS%2F8k%2B%2Bv7HsaCIKFPYU97OljVaOF8TTapetwS3Y%2FchQDXv8dhVlS9ZANVLs1zc5jJZqgxKQS5%2BAJLBiR6Tk44HKofrjNNYyvof%2FdtLpTIZpEfuFjTNE5AEg%3D%3D&biz_content=%7B%22body%22%3A%22%E6%9C%BA%E5%99%A8%E4%BA%BA%E8%AE%A2%E5%8D%95%E6%94%AF%E4%BB%98%EF%BC%8C%E8%AE%A2%E5%8D%95%E5%8F%B7%EF%BC%9Ad0625652-f3ce-4737-9030-86ecb8853f55%22%2C%22out_trade_no%22%3A%22d0625652-f3ce-4737-9030-86ecb8853f55%22%2C%22product_code%22%3A%22FAST_INSTANT_TRADE_PAY%22%2C%22qr_pay_mode%22%3A%221%22%2C%22subject%22%3A%22%E8%AE%A2%E5%8D%95%E6%94%AF%E4%BB%98-d0625652-f3ce-4737-9030-86ecb8853f55%22%2C%22timeout_express%22%3A%2230m%22%2C%22total_amount%22%3A0.01%7D" class="pay-button alipay-button" target="_blank">
            💳 点击进行支付宝支付
        </a>
        
        <div class="info">
            <h4>📱 设备使用说明:</h4>
            <ol>
                <li>设备调用API获取支付链接</li>
                <li>设备渲染此页面或直接打开支付链接</li>
                <li>用户使用支付宝扫码或登录支付</li>
                <li>支付完成后，设备轮询查询支付状态</li>
            </ol>
            
            <h4>🔗 支付链接信息:</h4>
            <p><strong>链接长度:</strong> 1205 字符</p>
            <p><strong>域名:</strong> openapi-sandbox.dl.alipaydev.com</p>
            
            <details>
                <summary>查看完整支付链接</summary>
                <div class="url-display">https://openapi-sandbox.dl.alipaydev.com/gateway.do?timestamp=2025-07-31+16%3A03%3A54&app_id=9021000138652386&method=alipay.trade.page.pay&charset=utf-8&format=json&version=1.0&sign_type=RSA2&notify_url=http%3A%2F%2Flocalhost%3A8000%2Fapi%2Fv1%2Fshop%2Fpayment%2Fcallback%2Falipay&return_url=http%3A%2F%2Flocalhost%3A8000%2Fpayment%2Fsuccess&sign=qPi9aVCbHan3QXblEOGRhZeRIfTYMYJOJtn5ZZ7OibQvoG5dUXcYsX0f5OLE%2BY8Fdy2mZIynMuo5UwE65HRT9waOhZ786DwPNayYLAb7mhBWUVP5KskSPgJvKuo9M99D%2FWwd%2BCgjURCqBgcEIO2pDyXbuPtjWylo7X2kCdsaZmEcidWxmXtCpTgjvDZrEynOrYHNPWf8V6wjOXIse6oEQN9qHohjs6fvS%2F8k%2B%2Bv7HsaCIKFPYU97OljVaOF8TTapetwS3Y%2FchQDXv8dhVlS9ZANVLs1zc5jJZqgxKQS5%2BAJLBiR6Tk44HKofrjNNYyvof%2FdtLpTIZpEfuFjTNE5AEg%3D%3D&biz_content=%7B%22body%22%3A%22%E6%9C%BA%E5%99%A8%E4%BA%BA%E8%AE%A2%E5%8D%95%E6%94%AF%E4%BB%98%EF%BC%8C%E8%AE%A2%E5%8D%95%E5%8F%B7%EF%BC%9Ad0625652-f3ce-4737-9030-86ecb8853f55%22%2C%22out_trade_no%22%3A%22d0625652-f3ce-4737-9030-86ecb8853f55%22%2C%22product_code%22%3A%22FAST_INSTANT_TRADE_PAY%22%2C%22qr_pay_mode%22%3A%221%22%2C%22subject%22%3A%22%E8%AE%A2%E5%8D%95%E6%94%AF%E4%BB%98-d0625652-f3ce-4737-9030-86ecb8853f55%22%2C%22timeout_express%22%3A%2230m%22%2C%22total_amount%22%3A0.01%7D</div>
            </details>
        </div>
    </div>
    
    <script>
        // 模拟设备端JavaScript
        console.log('设备支付页面加载完成');
        console.log('订单ID:', 'd0625652-f3ce-4737-9030-86ecb8853f55');
        console.log('支付链接长度:', 1205);
        
        // 可以添加设备端特定的逻辑
        function checkPaymentStatus() {
            fetch('/api/v1/orders/d0625652-f3ce-4737-9030-86ecb8853f55/payment-status')
                .then(response => response.json())
                .then(data => {
                    console.log('支付状态:', data.status);
                    if (data.status === 'paid') {
                        alert('支付成功！');
                    }
                })
                .catch(error => console.error('查询支付状态失败:', error));
        }
        
        // 每5秒查询一次支付状态（仅用于演示）
        setInterval(checkPaymentStatus, 5000);
    </script>
</body>
</html>

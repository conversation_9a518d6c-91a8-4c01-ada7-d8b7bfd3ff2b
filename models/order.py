from sqlmodel import SQLModel, <PERSON>
from typing import Optional
from datetime import datetime
import uuid


class Order(SQLModel, table=True):
    __tablename__ = "orders"
    
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    device_id: str = Field(foreign_key="devices.id")
    total_amount: float
    status: str = Field(default="pending_payment")  # pending_payment, paid, processing, completed, cancelled
    payment_transaction_id: Optional[str] = Field(default=None, foreign_key="payment_transactions.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class OrderItem(SQLModel, table=True):
    __tablename__ = "order_items"
    
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    order_id: str = Field(foreign_key="orders.id")
    product_id: str = Field(foreign_key="products.id")
    quantity: int
    price_at_purchase: float

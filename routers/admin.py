from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from typing import List

from database import get_session
from schemas.user import UserResponse, Token
from schemas.device import DeviceResponse
from schemas.order import OrderResponse
from services.auth_service import AuthService
from dependencies import require_admin
from models.user import User
from models.user_device_binding import Device
from models.order import Order
from utils.security import create_access_token
from utils.logger import get_logger, get_log_info, cleanup_old_logs
from config import settings
from datetime import timedelta

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/admin", tags=["Admin Operations"])


@router.post("/login", response_model=Token)
async def admin_login(
    user_login: dict,
    session: Session = Depends(get_session)
):
    """Admin login endpoint"""
    auth_service = AuthService(session)
    user = auth_service.authenticate_user(user_login["username"], user_login["password"])
    
    if not user or user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials"
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.id, "username": user.username, "role": user.role},
        expires_delta=access_token_expires
    )
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60,
        user_id=user.id,
        username=user.username,
        role=user.role
    )


@router.get("/users", response_model=List[UserResponse])
async def get_all_users(
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get all users"""
    statement = select(User)
    users = session.exec(statement).all()
    
    return [
        UserResponse(
            id=user.id,
            username=user.username,
            role=user.role,
            is_active=user.is_active,
            created_at=user.created_at
        )
        for user in users
    ]


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user_details(
    user_id: str,
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get user details"""
    statement = select(User).where(User.id == user_id)
    user = session.exec(statement).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return UserResponse(
        id=user.id,
        username=user.username,
        role=user.role,
        is_active=user.is_active,
        created_at=user.created_at
    )


@router.get("/devices", response_model=List[DeviceResponse])
async def get_all_devices(
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get all devices"""
    statement = select(Device)
    devices = session.exec(statement).all()
    
    return [
        DeviceResponse(
            device_id=device.device_id,
            device_name=device.device_name,
            status=device.status,
            last_heartbeat_at=device.last_heartbeat_at
        )
        for device in devices
    ]


@router.get("/orders", response_model=List[OrderResponse])
async def get_all_orders(
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get all orders"""
    statement = select(Order)
    orders = session.exec(statement).all()
    
    return [
        OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            created_at=order.created_at
        )
        for order in orders
    ]


@router.get("/system_status")
async def get_system_status(
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get system overview status"""
    
    # Count active devices
    active_devices_stmt = select(Device).where(Device.status == "online")
    active_devices_count = len(session.exec(active_devices_stmt).all())
    
    # Count pending orders
    pending_orders_stmt = select(Order).where(Order.status.in_(["pending_payment", "paid"]))
    pending_orders_count = len(session.exec(pending_orders_stmt).all())
    
    # Count total users
    total_users_stmt = select(User)
    total_users_count = len(session.exec(total_users_stmt).all())
    
    return {
        "active_devices": active_devices_count,
        "pending_orders": pending_orders_count,
        "total_users": total_users_count,
        "system_status": "healthy"
    }


@router.get("/logs/info")
async def get_logs_info(current_user: User = Depends(require_admin)):
    """获取日志配置和状态信息"""
    try:
        log_info = get_log_info()
        return {
            "success": True,
            "data": log_info
        }
    except Exception as e:
        logger.error(f"获取日志信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志信息失败: {str(e)}")


@router.post("/logs/cleanup")
async def cleanup_logs(current_user: User = Depends(require_admin)):
    """手动清理旧日志文件"""
    try:
        cleanup_old_logs()
        log_info = get_log_info()
        return {
            "success": True,
            "message": "日志清理完成",
            "data": log_info
        }
    except Exception as e:
        logger.error(f"清理日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理日志失败: {str(e)}")

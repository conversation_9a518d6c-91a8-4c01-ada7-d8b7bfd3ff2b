from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session
from typing import List

from database import get_session
from schemas.order import OrderResponse, OrderStatusUpdate
from services.shop_service import ShopService
from dependencies import require_staff
from models.user import User
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/staff", tags=["Staff Operations"])


@router.get("/orders/pending", response_model=List[OrderResponse])
async def get_pending_orders(
    current_user: User = Depends(require_staff),
    session: Session = Depends(get_session)
):
    """Get pending orders for staff processing"""
    shop_service = ShopService(session)
    orders = shop_service.get_pending_orders()
    
    return [
        OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            created_at=order.created_at
        )
        for order in orders
    ]


@router.put("/orders/{order_id}/status")
async def update_order_status(
    order_id: str,
    status_update: OrderStatusUpdate,
    current_user: User = Depends(require_staff),
    session: Session = Depends(get_session)
):
    """Update order processing status"""
    shop_service = ShopService(session)
    
    # Validate status
    valid_statuses = ["processing", "completed", "cancelled"]
    if status_update.status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {valid_statuses}"
        )
    
    success = shop_service.update_order_status(order_id, status_update.status)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )
    
    logger.info(f"Staff {current_user.username} updated order {order_id} to {status_update.status}")
    
    return {"message": "Order status updated successfully"}

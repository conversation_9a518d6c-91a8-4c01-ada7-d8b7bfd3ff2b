from fastapi import APIRouter

from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/shop", tags=["Order"])


@router.post('/orders')
async def orders():
    """
    生成系统内部订单
    """
    return {"message": "Order created"}


@router.get('/orders/{order_id}')
async def order_details(order_id: str):
    """
    创建订单,获取订单基本信息
    """
    return {"message": f"Order {order_id} details"}


@router.post('/orders/{order_id}/pay')
async def order_pay(order_id: str):
    """
    获取支付二维码,调用支付平台生成二维码
    """
    return {"message": f"Order {order_id} payment"}


@router.post('/orders/{order_id}/pay')
async def orders_pay(order_id: str):
    """
    获取支付二维码,调用支付平台生成二维码
    """
    return {"message": f"Order {order_id} payment"}


@router.get('/orders/{order_id}/payment-status')
async def orders_payment_status(order_id: str):
    """
    支付状态查询,供前端轮询支付状态
    """
    return {"message": f"Order {order_id} payment"}

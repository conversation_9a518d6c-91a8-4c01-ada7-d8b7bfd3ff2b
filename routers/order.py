from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session
from typing import Optional

from database import get_session
from services.shop_service import ShopService
from services.payment import PaymentFactory, PaymentMethod, PaymentRequest
from schemas.order import OrderCreate, OrderResponse, PaymentInitRequest, PaymentResponse
from utils.logger import get_logger
from config import settings

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/orders", tags=["Order"])


@router.post('/', response_model=OrderResponse)
async def create_order(
    order_data: OrderCreate,
    session: Session = Depends(get_session)
):
    """
    创建订单

    根据序列图步骤1: 设备创建订单，返回订单ID
    """
    try:
        shop_service = ShopService(session, settings.payment_config)

        # 创建订单
        order = shop_service.create_order_from_items(
            device_id=order_data.device_id,
            items=order_data.items,
            total_amount=order_data.total_amount
        )

        if not order:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建订单失败"
            )

        logger.info(f"Order {order.id} created for device {order_data.device_id}")

        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            created_at=order.created_at,
            items=[]  # 可以根据需要填充订单项
        )

    except Exception as e:
        logger.error(f"Error creating order: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建订单时发生错误: {str(e)}"
        )


@router.get('/{order_id}', response_model=OrderResponse)
async def get_order_details(
    order_id: str,
    session: Session = Depends(get_session)
):
    """
    获取订单详情
    """
    try:
        shop_service = ShopService(session)
        order = shop_service.get_order_by_id(order_id)

        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            created_at=order.created_at,
            items=[]  # 可以根据需要填充订单项
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting order details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订单详情时发生错误: {str(e)}"
        )


@router.post('/{order_id}/pay', response_model=PaymentResponse)
async def initiate_payment(
    order_id: str,
    payment_request: PaymentInitRequest,
    session: Session = Depends(get_session)
):
    """
    发起支付

    根据序列图步骤2-5: 设备发起支付，调用支付平台API，返回二维码URL
    """
    try:
        shop_service = ShopService(session, settings.payment_config)

        # 获取订单信息
        order = shop_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 检查订单状态
        if order.status != "pending_payment":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"订单状态不允许支付，当前状态: {order.status}"
            )

        # 验证支付方式
        try:
            payment_method = PaymentMethod(payment_request.payment_method)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的支付方式: {payment_request.payment_method}"
            )

        # 获取支付配置
        method_config = settings.payment_config.get(payment_request.payment_method, {})
        if not method_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"支付方式 {payment_request.payment_method} 未配置"
            )

        # 创建支付适配器
        payment_adapter = PaymentFactory.create_adapter(payment_method, method_config)

        # 创建支付请求
        pay_request = PaymentRequest(
            out_trade_no=order.id,
            total_amount=order.total_amount,
            subject=f"订单支付-{order.id}",
            body=f"机器人订单支付，订单号：{order.id}",
            notify_url=method_config.get('notify_url'),
            return_url=method_config.get('return_url')
        )

        # 调用支付平台API
        payment_result = payment_adapter.create_payment(pay_request)

        if not payment_result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"创建支付订单失败: {payment_result.message}"
            )

        logger.info(f"Payment initiated for order {order_id} with method {payment_request.payment_method}")

        return PaymentResponse(
            success=True,
            payment_url=payment_result.payment_url,
            qr_code=payment_result.qr_code,
            order_id=order_id,
            payment_method=payment_request.payment_method,
            message="支付订单创建成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initiating payment for order {order_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发起支付时发生错误: {str(e)}"
        )


@router.get('/{order_id}/payment-status')
async def get_payment_status(
    order_id: str,
    session: Session = Depends(get_session)
):
    """
    查询支付状态

    根据序列图步骤11: 设备轮询查询支付状态
    """
    try:
        shop_service = ShopService(session)

        # 获取订单信息
        order = shop_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        return {
            "order_id": order_id,
            "status": order.status,
            "total_amount": order.total_amount,
            "created_at": order.created_at,
            "updated_at": order.updated_at
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting payment status for order {order_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询支付状态时发生错误: {str(e)}"
        )

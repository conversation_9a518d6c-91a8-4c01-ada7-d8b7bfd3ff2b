from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlmodel import Session
import json

from database import get_session
from services.shop_service import ShopService
from services.payment import PaymentFactory, PaymentMethod
from utils.logger import get_logger
from config import settings

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/shop", tags=["Shop & Payment"])


@router.post("/payment/callback/{payment_method}")
async def payment_callback(payment_method: str, request: Request, session: Session = Depends(get_session)):
    """Handle payment platform callback"""
    try:
        # Get callback data
        if request.headers.get("content-type") == "application/json":
            callback_data = await request.json()
        else:
            form_data = await request.form()
            callback_data = dict(form_data)

        logger.info(f"Received {payment_method} payment callback: {callback_data}")

        # 验证支付方式
        try:
            payment_method_enum = PaymentMethod(payment_method)
        except ValueError:
            logger.error(f"Unsupported payment method: {payment_method}")
            return {"error": "Unsupported payment method"}

        # 获取支付配置
        payment_config = getattr(settings, 'payment_config', {})
        method_config = payment_config.get(payment_method, {})

        if not method_config:
            logger.error(f"No config found for payment method {payment_method}")
            return {"error": "Payment method not configured"}

        # 创建支付适配器
        payment_adapter = PaymentFactory.create_adapter(payment_method_enum, method_config)

        # 验证回调签名
        if not payment_adapter.verify_callback(callback_data):
            logger.error(f"Invalid callback signature for {payment_method}")
            return {"error": "Invalid signature"}

        # 解析回调数据
        payment_result = payment_adapter.parse_callback(callback_data)

        if not payment_result.success:
            logger.error(f"Failed to parse callback: {payment_result.message}")
            return {"error": "Failed to parse callback"}

        # 更新订单状态
        shop_service = ShopService(session)

        if payment_result.trade_status.value in ["success"]:
            success = shop_service.update_order_status(payment_result.out_trade_no, "paid")
            if success:
                logger.info(f"Order {payment_result.out_trade_no} marked as paid")
                # TODO: Notify staff app about new paid order
            else:
                logger.error(f"Failed to update order {payment_result.out_trade_no} status")

        # 返回成功响应（格式取决于支付提供商）
        if payment_method == "alipay":
            return "success"  # 支付宝要求返回字符串
        else:
            return {"status": "success"}

    except Exception as e:
        logger.error(f"Error processing {payment_method} payment callback: {str(e)}")
        if payment_method == "alipay":
            return "fail"
        else:
            return {"status": "error", "message": str(e)}


@router.get("/payment/query/{payment_method}/{order_id}")
async def query_payment(payment_method: str, order_id: str, session: Session = Depends(get_session)):
    """查询支付状态"""
    try:
        # 验证支付方式
        try:
            payment_method_enum = PaymentMethod(payment_method)
        except ValueError:
            raise HTTPException(status_code=400, detail="Unsupported payment method")

        # 获取支付配置
        payment_config = getattr(settings, 'payment_config', {})
        method_config = payment_config.get(payment_method, {})

        if not method_config:
            raise HTTPException(status_code=400, detail="Payment method not configured")

        # 创建支付适配器
        payment_adapter = PaymentFactory.create_adapter(payment_method_enum, method_config)

        # 查询支付状态
        from services.payment.payment_models import PaymentQueryRequest
        query_request = PaymentQueryRequest(out_trade_no=order_id)
        query_result = payment_adapter.query_payment(query_request)

        return {
            "success": query_result.success,
            "trade_status": query_result.trade_status.value,
            "out_trade_no": query_result.out_trade_no,
            "trade_no": query_result.trade_no,
            "total_amount": query_result.total_amount,
            "message": query_result.message
        }

    except Exception as e:
        logger.error(f"Error querying payment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/payment/refund/{payment_method}")
async def refund_payment(payment_method: str, refund_data: dict, session: Session = Depends(get_session)):
    """申请退款"""
    try:
        # 验证支付方式
        try:
            payment_method_enum = PaymentMethod(payment_method)
        except ValueError:
            raise HTTPException(status_code=400, detail="Unsupported payment method")

        # 获取支付配置
        payment_config = getattr(settings, 'payment_config', {})
        method_config = payment_config.get(payment_method, {})

        if not method_config:
            raise HTTPException(status_code=400, detail="Payment method not configured")

        # 创建支付适配器
        payment_adapter = PaymentFactory.create_adapter(payment_method_enum, method_config)

        # 申请退款
        from services.payment.payment_models import RefundRequest
        refund_request = RefundRequest(
            out_trade_no=refund_data.get('out_trade_no'),
            trade_no=refund_data.get('trade_no'),
            refund_amount=refund_data.get('refund_amount'),
            refund_reason=refund_data.get('refund_reason', '用户申请退款'),
            out_request_no=refund_data.get('out_request_no')
        )

        refund_result = payment_adapter.refund_payment(refund_request)

        return {
            "success": refund_result.success,
            "out_trade_no": refund_result.out_trade_no,
            "trade_no": refund_result.trade_no,
            "refund_fee": refund_result.refund_fee,
            "message": refund_result.message
        }

    except Exception as e:
        logger.error(f"Error processing refund: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post('/payment/notify/{channel}')
async def payment_notify(order_id: str):
    """
    支付结果回调,接收支付平台异步通知
    """
    return {"message": f"Order {order_id} payment"}

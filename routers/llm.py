from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session

from database import get_session
from schemas.llm import LLMIntentRequest, LLMIntentResponse, ShoppingListItem
from services.shop_service import ShopService
from services.payment.payment_models import PaymentMethod
from dependencies import get_current_device
from utils.logger import get_logger
from config import settings

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/llm", tags=["LLM Integration"])


@router.post("/process_intent", response_model=LLMIntentResponse)
async def process_intent(
    intent_request: LLMIntentRequest,
    device_id: str = Depends(get_current_device),
    session: Session = Depends(get_session)
):
    """Process LLM intent and handle shopping requests"""
    
    # Verify device_id matches token
    if intent_request.device_id != device_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Device ID mismatch"
        )
    
    shop_service = ShopService(session, settings.payment_config)
    
    if intent_request.intent_type == "cooking_recipe":
        # Process cooking recipe intent
        ingredients = [
            {
                "name": ing.name,
                "quantity": ing.quantity,
                "unit": ing.unit
            }
            for ing in intent_request.structured_data.ingredients
        ]
        
        # Match ingredients to products
        shopping_list = shop_service.match_ingredients_to_products(ingredients)
        
        if not shopping_list:
            logger.warning(f"No products found for recipe: {intent_request.structured_data.recipe_name}")
            return LLMIntentResponse(
                message="抱歉，没有找到相关的商品。",
                action_required="provide_answer"
            )
        
        # Create order
        order = shop_service.create_order(device_id, shopping_list)
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create order"
            )
        
        # Generate payment QR code (默认使用支付宝)
        payment_qr_url = shop_service.generate_payment_qr_code(order, PaymentMethod.ALIPAY)
        
        total_amount = sum(item.total_price for item in shopping_list)
        
        logger.info(f"Created order {order.id} for device {device_id} with total {total_amount}")
        
        return LLMIntentResponse(
            message=f"已为您找到{intent_request.structured_data.recipe_name}的食材，总计{total_amount}元。请扫码支付。",
            action_required="display_shopping_list",
            shopping_list=shopping_list,
            total_amount=total_amount,
            payment_qr_code_url=payment_qr_url
        )
    
    elif intent_request.intent_type == "general_query":
        # Handle general queries
        logger.info(f"General query from device {device_id}: {intent_request.raw_query}")
        
        return LLMIntentResponse(
            message="我已经收到您的询问，正在为您查找相关信息。",
            action_required="provide_answer"
        )
    
    else:
        # Unknown intent type
        logger.warning(f"Unknown intent type: {intent_request.intent_type}")
        
        return LLMIntentResponse(
            message="抱歉，我不理解您的请求。",
            action_required="provide_answer"
        )

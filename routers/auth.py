from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from sqlmodel import Session
from datetime import timedelta

from database import get_session
from schemas.user import UserLogin, Token, UserCreate, UserResponse
from services.auth_service import AuthService
from utils.security import create_access_token
from config import settings
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/auth", tags=["Authentication"])


@router.post("/login", response_model=Token)
async def login(
    user_login: UserLogin,
    session: Session = Depends(get_session)
):
    """User login endpoint"""
    auth_service = AuthService(session)
    user = auth_service.authenticate_user(user_login.username, user_login.password)
    
    if not user:
        logger.warning(f"Failed login attempt for username: {user_login.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.id, "username": user.username, "role": user.role},
        expires_delta=access_token_expires
    )
    
    logger.info(f"User {user.username} logged in successfully")
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60,
        user_id=user.id,
        username=user.username,
        role=user.role
    )


@router.post("/register", response_model=UserResponse)
async def register(
    user_create: UserCreate,
    session: Session = Depends(get_session)
):
    """User registration endpoint"""
    auth_service = AuthService(session)
    user = auth_service.create_user(
        username=user_create.username,
        password=user_create.password,
        role=user_create.role
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    logger.info(f"New user registered: {user.username}")
    
    return UserResponse(
        id=user.id,
        username=user.username,
        role=user.role,
        is_active=user.is_active,
        created_at=user.created_at
    )

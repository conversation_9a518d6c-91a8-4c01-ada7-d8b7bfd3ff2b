from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session
from typing import List
from datetime import datetime, timedelta

from database import get_session
from schemas.device import (
    DeviceRegister, DeviceRegisterResponse, DeviceHeartbeat, 
    DeviceHeartbeatResponse, DeviceBindAccess, DeviceBindResponse, DeviceResponse
)
from services.device_service import DeviceService
from dependencies import get_current_user, get_current_device
from models.user import User
from utils.security import create_access_token
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/device", tags=["Device Management"])


@router.post("/register", response_model=DeviceRegisterResponse)
async def register_device(
    device_data: DeviceRegister,
    session: Session = Depends(get_session)
):
    """Register a new Orange Pi device"""
    device_service = DeviceService(session)
    device = device_service.register_device(
        device_id=device_data.device_id,
        device_name=device_data.device_name,
        device_type=device_data.device_type
    )
    
    if not device:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to register device"
        )
    
    # Create device token
    device_token = create_access_token(
        data={"device_id": device.device_id, "type": "device"},
        expires_delta=timedelta(days=365)  # Long-lived token for devices
    )
    
    logger.info(f"Device {device.device_id} registered successfully")
    
    return DeviceRegisterResponse(
        message="Device registered successfully",
        device_token=device_token,
        device_id=device.device_id,
        binding_qr_code_data=device.binding_qr_code_data
    )


@router.post("/heartbeat", response_model=DeviceHeartbeatResponse)
async def device_heartbeat(
    heartbeat_data: DeviceHeartbeat,
    device_id: str = Depends(get_current_device),
    session: Session = Depends(get_session)
):
    """Update device heartbeat and status"""
    device_service = DeviceService(session)
    success = device_service.update_heartbeat(
        device_id=device_id,
        status=heartbeat_data.status,
        battery_level=heartbeat_data.battery_level,
        firmware_version=heartbeat_data.firmware_version
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    return DeviceHeartbeatResponse(
        message="Heartbeat updated successfully",
        timestamp=datetime.utcnow()
    )


@router.post("/bind_access", response_model=DeviceBindResponse)
async def bind_device_access(
    bind_data: DeviceBindAccess,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """Bind WebRTC App user to device using QR code"""
    device_service = DeviceService(session)
    device = device_service.bind_user_to_device(
        user_id=current_user.id,
        qr_code_data=bind_data.qr_code_data
    )
    
    if not device:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid QR code or device not found"
        )
    
    logger.info(f"User {current_user.username} bound to device {device.device_id}")
    
    return DeviceBindResponse(
        message="Device access granted successfully",
        device_id=device.device_id,
        user_id=current_user.id
    )


@router.get("/bound_devices", response_model=List[DeviceResponse])
async def get_bound_devices(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """Get all devices accessible by the current user"""
    device_service = DeviceService(session)
    devices = device_service.get_user_devices_legacy(current_user.id)
    
    return [
        DeviceResponse(
            device_id=device.device_id,
            device_name=device.device_name,
            status=device.status,
            last_heartbeat_at=device.last_heartbeat_at
        )
        for device in devices
    ]


@router.get("/bound_users")
async def get_bound_users(
    device_id: str = Depends(get_current_device),
    session: Session = Depends(get_session)
):
    """Get all users bound to the current device"""
    try:
        device_service = DeviceService(session)
        # Get all users that have access to this device
        bound_users = device_service.get_device_bound_users(device_id)

        return {
            "bound_users": [
                {
                    "user_id": user.id,
                    "username": user.username,
                    "role": user.role,
                    "access_granted_at": access.bound_at.isoformat() if access.bound_at else None
                }
                for user, access in bound_users
            ]
        }
    except Exception as e:
        logger.error(f"Error getting bound users for device {device_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get bound users")

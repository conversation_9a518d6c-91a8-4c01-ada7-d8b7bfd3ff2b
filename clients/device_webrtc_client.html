<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备端 - WebRTC 视频通话</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            text-align: center;
            opacity: 0.8;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #fff;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        button:disabled {
            background: rgba(255,255,255,0.3);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-success { 
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        .btn-danger { 
            background: linear-gradient(45deg, #e17055, #d63031);
        }
        .btn-warning { 
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }
        
        .log {
            background: rgba(0, 0, 0, 0.7);
            color: #0f0;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .error { color: #ff6b6b; }
        .success { color: #00b894; }
        .info { color: #74b9ff; }
        .warning { color: #fdcb6e; }
        
        input, select {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            font-size: 16px;
        }
        .status.connected {
            background: linear-gradient(45deg, #00b894, #00cec9);
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }
        .status.disconnected {
            background: linear-gradient(45deg, #e17055, #d63031);
            box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
        }
        .status.calling {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            box-shadow: 0 4px 15px rgba(253, 203, 110, 0.3);
            animation: pulse 2s infinite;
        }
        .status.incall {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .video-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }
        .video-row {
            display: flex;
            gap: 15px;
        }
        .video-box {
            flex: 1;
            position: relative;
            background: #000000; /* 改为纯黑色背景 */
            border-radius: 15px;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.2);
            min-height: 200px;
        }
        .video-box video {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 13px;
            background: #000000; /* 视频元素也设置黑色背景 */
        }
        .video-box.no-stream {
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }
        .video-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .call-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .device-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .device-info .info-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stats {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }
        
        .icon {
            font-size: 1.2em;
            margin-right: 5px;
        }

        /* 设备绑定样式 */
        .binding-content {
            text-align: center;
            padding: 20px;
        }

        .binding-code-display {
            margin-bottom: 20px;
        }

        .binding-code {
            font-size: 36px;
            font-weight: bold;
            color: #007bff;
            letter-spacing: 8px;
            margin-bottom: 10px;
            padding: 15px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 10px;
            border: 2px dashed #007bff;
        }

        .binding-instructions {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .qr-code-container {
            margin: 20px 0;
        }

        .qr-instructions {
            color: #666;
            font-size: 12px;
            margin-top: 10px;
        }

        .binding-timer {
            color: #ff6b6b;
            font-size: 12px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能设备端</h1>
        <p class="subtitle">WebRTC 视频通话系统 - 设备控制台</p>
        
        <div class="device-info">
            <div class="info-item">
                <span>🏷️ 设备类型:</span>
                <span>Orange Pi 智能摄像头</span>
            </div>
            <div class="info-item">
                <span>📡 连接状态:</span>
                <span id="networkStatus">离线</span>
            </div>
            <div class="info-item">
                <span>🔋 电源状态:</span>
                <span>外接电源</span>
            </div>
            <div class="info-item">
                <span>🌡️ 设备温度:</span>
                <span id="deviceTemp">42°C</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h3><span class="icon">🔧</span>设备配置</h3>
            <div>
                <label>设备ID:</label>
                <input type="text" id="deviceId" value="smart_camera_001" placeholder="输入设备唯一标识">
            </div>
            <div>
                <label>设备名称:</label>
                <input type="text" id="deviceName" value="客厅智能摄像头" placeholder="设备显示名称">
            </div>
            <div>
                <label>设备位置:</label>
                <input type="text" id="deviceLocation" value="客厅" placeholder="设备安装位置">
            </div>
            <button onclick="deviceRegister()" id="deviceRegisterBtn" class="btn-success">📝 注册设备</button>
            <button onclick="connectDevice()" id="deviceConnectBtn" disabled class="btn-warning">🔗 连接服务器</button>
            <button onclick="disconnectDevice()" id="deviceDisconnectBtn" disabled class="btn-danger">❌ 断开连接</button>
        </div>

        <div class="status disconnected" id="deviceStatus">
            🔴 设备离线
        </div>

        <div class="section">
            <h3><span class="icon">📹</span>视频监控</h3>
            <div class="video-container">
                <div class="video-row">
                    <div class="video-box">
                        <div class="video-label">📹 本地摄像头</div>
                        <video id="deviceLocalVideo" autoplay muted playsinline></video>
                    </div>
                    <div class="video-box">
                        <div class="video-label">📱 远程用户</div>
                        <video id="deviceRemoteVideo" autoplay playsinline></video>
                    </div>
                </div>
            </div>
            
            <div class="call-controls">
                <button onclick="startDeviceCamera()" id="deviceStartCameraBtn" disabled class="btn-success">
                    📹 启动摄像头
                </button>
                <button onclick="answerCall()" id="deviceAnswerBtn" disabled class="btn-warning">
                    📞 接听通话
                </button>
                <button onclick="hangupDevice()" id="deviceHangupBtn" disabled class="btn-danger">
                    📞 挂断通话
                </button>
            </div>
        </div>

        <div class="section">
            <h3><span class="icon">⚙️</span>设备控制</h3>
            <div class="call-controls">
                <button onclick="toggleDeviceVideo()" id="deviceToggleVideoBtn" disabled>
                    📹 关闭视频
                </button>
                <button onclick="toggleDeviceAudio()" id="deviceToggleAudioBtn" disabled>
                    🎤 静音
                </button>
                <button onclick="switchDeviceCamera()" id="deviceSwitchCameraBtn" disabled>
                    🔄 切换摄像头
                </button>
                <button onclick="takeSnapshot()" id="deviceSnapshotBtn" disabled>
                    📸 拍照
                </button>
            </div>
            
            <div class="stats" id="deviceStats">
                等待设备连接...
            </div>
        </div>

        <div class="section">
            <h3><span class="icon">📋</span>设备日志</h3>
            <div class="log" id="deviceLog">
                <div class="info">[系统] 设备控制台已启动</div>
                <div class="info">[系统] 等待设备注册...</div>
            </div>
            <button onclick="clearDeviceLog()">🗑️ 清空日志</button>
            <button onclick="exportLogs()">💾 导出日志</button>
            <button onclick="diagnoseVideoStream()">🔍 视频诊断</button>
            <button onclick="generateBindingCode()">🔗 生成绑定码</button>
        </div>

        <!-- 设备绑定码面板 -->
        <div class="section" id="bindingPanel" style="display: none;">
            <h3><span class="icon">🔗</span>设备绑定</h3>
            <div class="binding-content">
                <div class="binding-code-display">
                    <div class="binding-code" id="bindingCode">------</div>
                    <div class="binding-instructions">
                        请在手机App中输入此6位绑定码
                    </div>
                </div>
                <div class="qr-code-container" id="qrCodeContainer">
                    <img id="qrCodeImage" src="" alt="绑定二维码" style="display: none; max-width: 200px;">
                    <div class="qr-instructions">或扫描二维码快速绑定</div>
                </div>
                <div class="binding-timer" id="bindingTimer">
                    绑定码有效期：30分钟
                </div>
                <button onclick="hideBindingCode()">❌ 关闭</button>
            </div>
        </div>
    </div>

    <script>
        // WebRTC配置
        const ICE_SERVERS = [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:*************:3478' },
            { 
                urls: 'turn:*************:3478',
                username: 'username1',
                credential: 'password1'
            }
        ];

        // 全局变量
        let deviceSocket = null;
        let deviceConnected = false;
        let devicePeerConnection = null;
        let deviceLocalStream = null;
        let deviceToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOiJzbWFydF9jYW1lcmFfMDAxIiwidHlwZSI6ImRldmljZSIsImV4cCI6MTc4NTM4ODg3OH0.WwAXKY2S1hihXFFi0pi2dMeUMQZ6JYo7CnSWOkBsHZo";
        let currentDeviceId = "smart_camera_001";

        // 服务器配置
        const SERVER_BASE_URL = 'http://*************:8000';
        
        // 设备状态
        let deviceCallState = 'idle'; // idle, ringing, incall
        let deviceVideoEnabled = true;
        let deviceAudioEnabled = true;

        function addLog(message, type = 'info') {
            const log = document.getElementById('deviceLog');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = type;
            entry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function updateStatus(message, status) {
            const statusElement = document.getElementById('deviceStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${status}`;
            
            // 更新网络状态
            const networkStatus = document.getElementById('networkStatus');
            networkStatus.textContent = status === 'connected' ? '在线' : '离线';
        }

        function updateDeviceButtons() {
            const connected = deviceConnected;
            const hasMedia = deviceLocalStream !== null;
            const inCall = deviceCallState === 'incall';
            const ringing = deviceCallState === 'ringing';
            
            document.getElementById('deviceRegisterBtn').disabled = deviceToken !== null;
            document.getElementById('deviceConnectBtn').disabled = !deviceToken || connected;
            document.getElementById('deviceDisconnectBtn').disabled = !connected;
            document.getElementById('deviceStartCameraBtn').disabled = !connected || hasMedia;
            document.getElementById('deviceAnswerBtn').disabled = !ringing;
            document.getElementById('deviceHangupBtn').disabled = !ringing && !inCall;
            document.getElementById('deviceToggleVideoBtn').disabled = !hasMedia;
            document.getElementById('deviceToggleAudioBtn').disabled = !hasMedia;
            document.getElementById('deviceSwitchCameraBtn').disabled = !hasMedia;
            document.getElementById('deviceSnapshotBtn').disabled = !hasMedia;
        }

        // 模拟设备温度更新
        function updateDeviceTemp() {
            const temp = 40 + Math.random() * 10;
            document.getElementById('deviceTemp').textContent = temp.toFixed(1) + '°C';
        }

        // 初始化
        updateDeviceButtons();
        setInterval(updateDeviceTemp, 5000);
        
        // 设备注册
        async function deviceRegister() {
            const deviceId = document.getElementById('deviceId').value.trim();
            const deviceName = document.getElementById('deviceName').value.trim();

            if (!deviceId || !deviceName) {
                addLog('请输入设备ID和设备名称', 'error');
                return;
            }

            try {
                addLog('正在注册设备...', 'info');
                const response = await fetch(`${SERVER_BASE_URL}/api/v1/device/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        device_id: deviceId,
                        device_name: deviceName,
                        device_type: 'Orange Pi'
                    })
                });

                const data = await response.json();
                if (response.ok) {
                    deviceToken = data.device_token;
                    currentDeviceId = deviceId; // 保存设备ID到全局变量
                    addLog(`✅ 设备 ${deviceId} 注册成功`, 'success');
                    addLog(`设备Token: ${deviceToken.substring(0, 20)}...`, 'info');
                    updateDeviceButtons();
                } else {
                    addLog(`❌ 注册失败: ${data.detail}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 注册异常: ${error.message}`, 'error');
            }
        }

        // WebSocket连接
        function connectDevice() {
            if (!deviceToken) {
                addLog('请先注册设备', 'error');
                return;
            }

            const wsUrl = `ws://*************:8000/api/v1/webrtc/connect?token=${deviceToken}&participant_type=device&target_id=testuser&connection_type=video&features=messaging`;

            addLog('连接到服务器...', 'info');
            updateStatus('🟡 正在连接...', 'calling');

            deviceSocket = new WebSocket(wsUrl);

            deviceSocket.onopen = function(event) {
                deviceConnected = true;
                addLog('✅ 服务器连接成功', 'success');
                updateStatus('🟢 设备在线', 'connected');
                updateDeviceButtons();
                initDeviceWebRTC();
            };

            deviceSocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleDeviceMessage(data);
            };

            deviceSocket.onclose = function(event) {
                deviceConnected = false;
                if (event.code === 1000) {
                    addLog(`📞 通话已结束 (${event.code})`, 'info');
                } else {
                    addLog(`🔌 连接断开 (${event.code})`, 'warning');
                }
                updateStatus('🔴 设备离线', 'disconnected');
                updateDeviceButtons();
                cleanupDeviceWebRTC();
            };

            deviceSocket.onerror = function(error) {
                addLog(`❌ 连接错误`, 'error');
                updateStatus('🔴 连接错误', 'disconnected');
                updateDeviceButtons();
            };
        }

        function disconnectDevice() {
            if (deviceSocket) {
                deviceSocket.close();
                deviceSocket = null;
                addLog('主动断开连接', 'info');
            }
            // 防止立即重连
            setTimeout(() => {
                if (!deviceConnected) {
                    addLog('连接已完全断开', 'info');
                }
            }, 1000);
        }

        // WebRTC初始化
        function initDeviceWebRTC() {
            devicePeerConnection = new RTCPeerConnection({ iceServers: ICE_SERVERS });

            devicePeerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    addLog('🧊 发送ICE候选', 'info');
                    sendDeviceMessage({
                        type: 'candidate',
                        payload: {
                            candidate: event.candidate.candidate,
                            sdpMLineIndex: event.candidate.sdpMLineIndex,
                            sdpMid: event.candidate.sdpMid
                        }
                    });
                }
            };

            devicePeerConnection.ontrack = (event) => {
                addLog('📺 收到远程视频流', 'success');
                const remoteVideo = document.getElementById('deviceRemoteVideo');
                const stream = event.streams[0];

                if (stream && stream.getTracks().length > 0) {
                    remoteVideo.srcObject = stream;
                    addLog(`📺 远程流包含 ${stream.getVideoTracks().length} 个视频轨道和 ${stream.getAudioTracks().length} 个音频轨道`, 'info');

                    // 监听视频加载事件
                    remoteVideo.onloadedmetadata = () => {
                        addLog('📺 远程视频元数据已加载', 'success');
                    };

                    remoteVideo.onplay = () => {
                        addLog('📺 远程视频开始播放', 'success');
                    };

                    remoteVideo.onerror = (error) => {
                        addLog(`❌ 远程视频播放错误: ${error}`, 'error');
                    };
                } else {
                    addLog('⚠️ 收到空的远程流', 'warning');
                }

                updateDeviceStats();
            };

            devicePeerConnection.onconnectionstatechange = () => {
                const state = devicePeerConnection.connectionState;
                addLog(`🔗 连接状态: ${state}`, 'info');
                updateDeviceStats();

                if (state === 'connected') {
                    deviceCallState = 'incall';
                    updateStatus('📞 通话中', 'incall');
                    updateDeviceButtons();
                } else if (state === 'disconnected' || state === 'failed') {
                    hangupDevice();
                }
            };

            devicePeerConnection.oniceconnectionstatechange = () => {
                addLog(`🧊 ICE状态: ${devicePeerConnection.iceConnectionState}`, 'info');
                updateDeviceStats();
            };

            updateDeviceButtons();
        }

        function cleanupDeviceWebRTC() {
            if (devicePeerConnection) {
                devicePeerConnection.close();
                devicePeerConnection = null;
            }
            if (deviceLocalStream) {
                deviceLocalStream.getTracks().forEach(track => track.stop());
                deviceLocalStream = null;
            }
            document.getElementById('deviceLocalVideo').srcObject = null;
            document.getElementById('deviceRemoteVideo').srcObject = null;
            deviceCallState = 'idle';
            updateDeviceButtons();
            updateDeviceStats();
        }

        // 摄像头控制
        async function startDeviceCamera() {
            try {
                addLog('📹 启动摄像头...', 'info');

                // 检查浏览器支持
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('浏览器不支持摄像头访问，请使用Chrome/Firefox/Safari等现代浏览器');
                }

                deviceLocalStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        frameRate: { ideal: 30 }
                    },
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                document.getElementById('deviceLocalVideo').srcObject = deviceLocalStream;

                if (devicePeerConnection) {
                    deviceLocalStream.getTracks().forEach(track => {
                        devicePeerConnection.addTrack(track, deviceLocalStream);
                    });
                }

                addLog('✅ 摄像头启动成功', 'success');
                updateDeviceButtons();
                updateDeviceStats();
            } catch (error) {
                addLog(`❌ 摄像头启动失败: ${error.message}`, 'error');

                // 提供解决建议
                if (error.name === 'NotAllowedError') {
                    addLog('💡 请允许浏览器访问摄像头和麦克风', 'warning');
                } else if (error.name === 'NotFoundError') {
                    addLog('💡 未找到摄像头设备，请检查设备连接', 'warning');
                } else if (error.name === 'NotSupportedError') {
                    addLog('💡 请使用HTTPS访问或使用localhost', 'warning');
                }
            }
        }

        // 通话控制
        async function answerCall() {
            if (!deviceLocalStream) {
                addLog('请先启动摄像头', 'error');
                return;
            }

            try {
                addLog('📞 接听通话...', 'info');
                deviceCallState = 'incall';
                updateStatus('📞 通话中', 'incall');
                updateDeviceButtons();

                addLog('✅ 正在建立连接...', 'success');
            } catch (error) {
                addLog(`❌ 接听失败: ${error.message}`, 'error');
                deviceCallState = 'idle';
                updateStatus('🟢 设备在线', 'connected');
                updateDeviceButtons();
            }
        }

        function hangupDevice() {
            addLog('📞 主动挂断通话', 'info');

            sendDeviceMessage({
                type: 'hangup',
                payload: {}
            });

            deviceCallState = 'idle';
            updateStatus('🟢 设备在线', 'connected');

            // 清理WebRTC连接但不重新初始化
            if (devicePeerConnection) {
                devicePeerConnection.close();
                devicePeerConnection = null;
            }

            document.getElementById('deviceRemoteVideo').srcObject = null;
            updateDeviceButtons();
            updateDeviceStats();

            addLog('等待服务器断开连接...', 'info');
        }

        // 媒体控制
        function toggleDeviceVideo() {
            if (deviceLocalStream) {
                const videoTrack = deviceLocalStream.getVideoTracks()[0];
                if (videoTrack) {
                    deviceVideoEnabled = !deviceVideoEnabled;
                    videoTrack.enabled = deviceVideoEnabled;
                    document.getElementById('deviceToggleVideoBtn').textContent = deviceVideoEnabled ? '📹 关闭视频' : '📹 开启视频';
                    addLog(`📹 视频${deviceVideoEnabled ? '开启' : '关闭'}`, 'info');
                }
            }
        }

        function toggleDeviceAudio() {
            if (deviceLocalStream) {
                const audioTrack = deviceLocalStream.getAudioTracks()[0];
                if (audioTrack) {
                    deviceAudioEnabled = !deviceAudioEnabled;
                    audioTrack.enabled = deviceAudioEnabled;
                    document.getElementById('deviceToggleAudioBtn').textContent = deviceAudioEnabled ? '🎤 静音' : '🎤 取消静音';
                    addLog(`🎤 音频${deviceAudioEnabled ? '开启' : '静音'}`, 'info');
                }
            }
        }

        async function switchDeviceCamera() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');

                if (videoDevices.length > 1) {
                    const currentTrack = deviceLocalStream.getVideoTracks()[0];
                    const currentDeviceId = currentTrack.getSettings().deviceId;
                    const currentIndex = videoDevices.findIndex(device => device.deviceId === currentDeviceId);
                    const nextIndex = (currentIndex + 1) % videoDevices.length;
                    const nextDevice = videoDevices[nextIndex];

                    const newStream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: nextDevice.deviceId },
                        audio: true
                    });

                    const newVideoTrack = newStream.getVideoTracks()[0];
                    const sender = devicePeerConnection.getSenders().find(s =>
                        s.track && s.track.kind === 'video'
                    );

                    if (sender) {
                        await sender.replaceTrack(newVideoTrack);
                    }

                    currentTrack.stop();
                    deviceLocalStream.removeTrack(currentTrack);
                    deviceLocalStream.addTrack(newVideoTrack);

                    document.getElementById('deviceLocalVideo').srcObject = deviceLocalStream;
                    addLog('📷 摄像头已切换', 'success');
                } else {
                    addLog('只有一个摄像头设备', 'warning');
                }
            } catch (error) {
                addLog(`❌ 切换摄像头失败: ${error.message}`, 'error');
            }
        }

        function takeSnapshot() {
            if (deviceLocalStream) {
                const video = document.getElementById('deviceLocalVideo');
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0);

                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `snapshot_${new Date().getTime()}.jpg`;
                    a.click();
                    URL.revokeObjectURL(url);
                    addLog('📸 快照已保存', 'success');
                });
            }
        }

        // 消息处理
        function handleDeviceMessage(data) {
            const messageType = data.type;
            addLog(`📨 收到: ${messageType}`, 'success');

            if (messageType === 'connection_established') {
                addLog(`   会话ID: ${data.payload.session_id}`, 'info');
            } else if (messageType === 'peer_connected') {
                addLog(`   用户已连接: ${data.payload.peer_type}`, 'success');
            } else if (messageType === 'offer') {
                handleDeviceOffer(data.payload);
            } else if (messageType === 'answer') {
                handleDeviceAnswer(data.payload);
            } else if (messageType === 'candidate') {
                handleDeviceCandidate(data.payload);
            } else if (messageType === 'hangup') {
                handleDeviceHangup();
            }
        }

        // WebRTC信令处理
        async function handleDeviceOffer(payload) {
            try {
                addLog('📥 收到通话邀请', 'warning');
                deviceCallState = 'ringing';
                updateStatus('📞 来电中...', 'calling');
                updateDeviceButtons();

                const offer = new RTCSessionDescription(payload);
                await devicePeerConnection.setRemoteDescription(offer);

                if (deviceLocalStream) {
                    // 确保本地流已添加到 PeerConnection
                    deviceLocalStream.getTracks().forEach(track => {
                        const sender = devicePeerConnection.getSenders().find(s => s.track === track);
                        if (!sender) {
                            devicePeerConnection.addTrack(track, deviceLocalStream);
                            addLog(`📤 添加${track.kind}轨道到连接`, 'info');
                        }
                    });

                    const answer = await devicePeerConnection.createAnswer();
                    await devicePeerConnection.setLocalDescription(answer);

                    sendDeviceMessage({
                        type: 'answer',
                        payload: {
                            type: 'answer',
                            sdp: answer.sdp
                        }
                    });

                    // 调试信息：显示发送的轨道
                    const senders = devicePeerConnection.getSenders();
                    addLog(`📤 Answer包含 ${senders.length} 个发送器`, 'info');
                    senders.forEach((sender, index) => {
                        if (sender.track) {
                            addLog(`   发送器${index}: ${sender.track.kind} - ${sender.track.readyState}`, 'info');
                        }
                    });

                    addLog('✅ 自动接听，Answer已发送', 'success');
                    deviceCallState = 'incall';
                    updateStatus('📞 通话中', 'incall');
                    updateDeviceButtons();
                } else {
                    addLog('⚠️ 请先启动摄像头然后点击接听', 'warning');
                }
            } catch (error) {
                addLog(`❌ 处理Offer失败: ${error.message}`, 'error');
            }
        }

        async function handleDeviceAnswer(payload) {
            try {
                const answer = new RTCSessionDescription(payload);
                await devicePeerConnection.setRemoteDescription(answer);
                addLog('✅ Answer处理完成', 'success');
            } catch (error) {
                addLog(`❌ 处理Answer失败: ${error.message}`, 'error');
            }
        }

        async function handleDeviceCandidate(payload) {
            try {
                const candidate = new RTCIceCandidate(payload);
                await devicePeerConnection.addIceCandidate(candidate);
                addLog('🧊 ICE候选已添加', 'info');
            } catch (error) {
                addLog(`❌ 添加ICE候选失败: ${error.message}`, 'error');
            }
        }

        function handleDeviceHangup() {
            addLog('📞 对方挂断了通话', 'warning');

            deviceCallState = 'idle';
            updateStatus('🟢 设备在线', 'connected');

            // 清理WebRTC连接
            if (devicePeerConnection) {
                devicePeerConnection.close();
                devicePeerConnection = null;
            }

            document.getElementById('deviceRemoteVideo').srcObject = null;
            updateDeviceButtons();
            updateDeviceStats();

            addLog('通话已被对方结束', 'info');
        }

        // 发送消息
        function sendDeviceMessage(message) {
            if (deviceSocket && deviceConnected) {
                deviceSocket.send(JSON.stringify(message));
            }
        }

        // 统计信息更新
        function updateDeviceStats() {
            const statsElement = document.getElementById('deviceStats');
            let statsText = '';

            if (devicePeerConnection) {
                const state = devicePeerConnection.connectionState;
                const iceState = devicePeerConnection.iceConnectionState;
                statsText += `连接: ${state} | ICE: ${iceState}`;

                // 本地流信息
                if (deviceLocalStream) {
                    const videoTrack = deviceLocalStream.getVideoTracks()[0];
                    const audioTrack = deviceLocalStream.getAudioTracks()[0];
                    if (videoTrack) {
                        const settings = videoTrack.getSettings();
                        statsText += ` | 本地视频: ${settings.width}x${settings.height}@${settings.frameRate}fps`;
                        statsText += ` | 状态: ${videoTrack.readyState}`;
                    }
                    if (audioTrack) {
                        statsText += ` | 音频: ${audioTrack.enabled ? '开启' : '关闭'}`;
                    }
                }

                // 远程流信息
                const remoteVideo = document.getElementById('deviceRemoteVideo');
                if (remoteVideo.srcObject) {
                    const remoteStream = remoteVideo.srcObject;
                    const remoteVideoTrack = remoteStream.getVideoTracks()[0];
                    if (remoteVideoTrack) {
                        statsText += ` | 远程视频: ${remoteVideoTrack.readyState}`;
                        statsText += ` | 启用: ${remoteVideoTrack.enabled}`;
                    }
                }
            } else {
                statsText = '等待连接...';
            }

            statsElement.textContent = statsText;
        }

        // 工具函数
        function clearDeviceLog() {
            document.getElementById('deviceLog').innerHTML = '<div class="info">[系统] 日志已清空</div>';
        }

        function exportLogs() {
            const log = document.getElementById('deviceLog');
            const logText = Array.from(log.children).map(entry => entry.textContent).join('\n');

            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `device_logs_${new Date().getTime()}.txt`;
            a.click();
            URL.revokeObjectURL(url);

            addLog('📄 日志已导出', 'success');
        }

        // 视频流诊断函数
        function diagnoseVideoStream() {
            addLog('🔍 开始视频流诊断...', 'info');

            // 检查本地视频
            const localVideo = document.getElementById('deviceLocalVideo');
            if (localVideo.srcObject) {
                const localStream = localVideo.srcObject;
                addLog(`📹 本地流: ${localStream.getTracks().length} 个轨道`, 'info');
                localStream.getTracks().forEach((track, index) => {
                    addLog(`   轨道${index}: ${track.kind} - ${track.readyState} - ${track.enabled ? '启用' : '禁用'}`, 'info');
                });
            } else {
                addLog('❌ 本地视频流未设置', 'warning');
            }

            // 检查远程视频
            const remoteVideo = document.getElementById('deviceRemoteVideo');
            if (remoteVideo.srcObject) {
                const remoteStream = remoteVideo.srcObject;
                addLog(`📺 远程流: ${remoteStream.getTracks().length} 个轨道`, 'info');
                remoteStream.getTracks().forEach((track, index) => {
                    addLog(`   轨道${index}: ${track.kind} - ${track.readyState} - ${track.enabled ? '启用' : '禁用'}`, 'info');
                });

                // 检查视频元素状态
                addLog(`📺 远程视频元素: ${remoteVideo.videoWidth}x${remoteVideo.videoHeight}`, 'info');
                addLog(`📺 播放状态: paused=${remoteVideo.paused}, ended=${remoteVideo.ended}`, 'info');
            } else {
                addLog('❌ 远程视频流未设置', 'warning');
            }

            // 检查WebRTC连接
            if (devicePeerConnection) {
                addLog(`🔗 WebRTC连接: ${devicePeerConnection.connectionState}`, 'info');
                addLog(`🧊 ICE连接: ${devicePeerConnection.iceConnectionState}`, 'info');
            }
        }

        // 定期更新统计信息
        setInterval(() => {
            updateDeviceStats();
        }, 1000);

        // 浏览器兼容性检查
        function checkBrowserSupport() {
            const issues = [];

            if (!navigator.mediaDevices) {
                issues.push('不支持 MediaDevices API');
            }

            if (!navigator.mediaDevices?.getUserMedia) {
                issues.push('不支持 getUserMedia');
            }

            if (!window.RTCPeerConnection) {
                issues.push('不支持 WebRTC');
            }

            if (!window.WebSocket) {
                issues.push('不支持 WebSocket');
            }

            if (issues.length > 0) {
                addLog('⚠️ 浏览器兼容性问题:', 'warning');
                issues.forEach(issue => addLog(`   - ${issue}`, 'warning'));
                addLog('💡 建议使用 Chrome 88+, Firefox 85+, Safari 14+', 'info');

                if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                    addLog('💡 WebRTC需要HTTPS或localhost环境', 'warning');
                }
            } else {
                addLog('✅ 浏览器支持所有必需功能', 'success');
            }
        }

        // ==================== 设备绑定功能 ====================

        async function generateBindingCode() {
            if (!deviceToken) {
                addLog('❌ 请先注册设备', 'error');
                return;
            }

            if (!currentDeviceId) {
                addLog('❌ 设备ID未找到，请重新注册设备', 'error');
                return;
            }

            try {
                addLog('🔗 正在生成绑定码...', 'info');

                const url = `${SERVER_BASE_URL}/api/v1/webrtc_v2/device/${currentDeviceId}/generate-binding-code?expires_minutes=30`;
                addLog(`📍 请求URL: ${url}`, 'info');

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                addLog(`📊 响应状态: ${response.status} ${response.statusText}`, 'info');
                addLog(`📋 响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');

                if (!response.ok) {
                    const errorText = await response.text();
                    addLog(`❌ 响应内容: ${errorText}`, 'error');
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();

                if (result.success) {
                    const bindingData = result.data;
                    showBindingCode(bindingData);
                    addLog(`✅ 绑定码生成成功: ${bindingData.binding_code}`, 'success');
                } else {
                    throw new Error(result.message || '生成绑定码失败');
                }

            } catch (error) {
                addLog(`❌ 生成绑定码失败: ${error.message}`, 'error');
            }
        }

        function showBindingCode(bindingData) {
            // 显示绑定码面板
            document.getElementById('bindingPanel').style.display = 'block';

            // 设置绑定码
            document.getElementById('bindingCode').textContent = bindingData.binding_code;

            // 设置二维码
            if (bindingData.qr_code_data) {
                const qrImage = document.getElementById('qrCodeImage');
                qrImage.src = bindingData.qr_code_data;
                qrImage.style.display = 'block';
            }

            // 设置过期时间
            const expiresAt = new Date(bindingData.expires_at);
            updateBindingTimer(expiresAt);

            // 开始倒计时
            startBindingTimer(expiresAt);
        }

        function hideBindingCode() {
            document.getElementById('bindingPanel').style.display = 'none';

            // 清除倒计时
            if (window.bindingTimerInterval) {
                clearInterval(window.bindingTimerInterval);
                window.bindingTimerInterval = null;
            }
        }

        function updateBindingTimer(expiresAt) {
            const now = new Date();
            const timeLeft = expiresAt - now;

            if (timeLeft <= 0) {
                document.getElementById('bindingTimer').textContent = '绑定码已过期';
                document.getElementById('bindingCode').style.opacity = '0.5';
                return false;
            }

            const minutes = Math.floor(timeLeft / 60000);
            const seconds = Math.floor((timeLeft % 60000) / 1000);

            document.getElementById('bindingTimer').textContent =
                `绑定码有效期：${minutes}分${seconds}秒`;

            return true;
        }

        function startBindingTimer(expiresAt) {
            // 清除之前的定时器
            if (window.bindingTimerInterval) {
                clearInterval(window.bindingTimerInterval);
            }

            // 每秒更新一次
            window.bindingTimerInterval = setInterval(() => {
                if (!updateBindingTimer(expiresAt)) {
                    clearInterval(window.bindingTimerInterval);
                    window.bindingTimerInterval = null;
                }
            }, 1000);
        }

        checkBrowserSupport();
        addLog('设备控制台初始化完成', 'success');
        addLog('请先注册设备以开始使用', 'info');
    </script>
</body>
</html>

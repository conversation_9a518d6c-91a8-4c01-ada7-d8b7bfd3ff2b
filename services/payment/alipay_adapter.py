import logging
import json
from typing import Optional
from datetime import datetime

from alipay.aop.api.AlipayClientConfig import AlipayClientConfig
from alipay.aop.api.DefaultAlipayClient import DefaultAlipayClient
from alipay.aop.api.domain.AlipayTradePagePayModel import AlipayTradePagePayModel
from alipay.aop.api.domain.AlipayTradeQueryModel import AlipayTradeQueryModel
from alipay.aop.api.domain.AlipayTradeCloseModel import AlipayTradeCloseModel
from alipay.aop.api.domain.AlipayTradeRefundModel import AlipayTradeRefundModel
from alipay.aop.api.domain.AlipayTradeFastpayRefundQueryModel import AlipayTradeFastpayRefundQueryModel

from alipay.aop.api.request.AlipayTradePagePayRequest import AlipayTradePagePayRequest
from alipay.aop.api.request.AlipayTradeQueryRequest import AlipayTradeQueryRequest
from alipay.aop.api.request.AlipayTradeCloseRequest import AlipayTradeCloseRequest
from alipay.aop.api.request.AlipayTradeRefundRequest import AlipayTradeRefundRequest
from alipay.aop.api.request.AlipayTradeFastpayRefundQueryRequest import AlipayTradeFastpayRefundQueryRequest

from alipay.aop.api.response.AlipayTradePagePayResponse import AlipayTradePagePayResponse
from alipay.aop.api.response.AlipayTradeQueryResponse import AlipayTradeQueryResponse
from alipay.aop.api.response.AlipayTradeCloseResponse import AlipayTradeCloseResponse
from alipay.aop.api.response.AlipayTradeRefundResponse import AlipayTradeRefundResponse
from alipay.aop.api.response.AlipayTradeFastpayRefundQueryResponse import AlipayTradeFastpayRefundQueryResponse

from .payment_adapter import PaymentAdapter
from .payment_models import (
    PaymentRequest, PaymentResponse, PaymentQueryRequest, PaymentQueryResponse,
    RefundRequest, RefundResponse, RefundQueryRequest, RefundQueryResponse,
    PaymentStatus
)
from utils.logger import get_logger

logger = get_logger(__name__)


class AlipayAdapter(PaymentAdapter):
    """支付宝支付适配器"""
    
    def __init__(self, config: dict):
        super().__init__(config)
        
        # 初始化支付宝客户端配置
        alipay_config = AlipayClientConfig()
        alipay_config.server_url = config.get('server_url')
        alipay_config.app_id = config.get('app_id')
        alipay_config.app_private_key = config.get('app_private_key')
        alipay_config.alipay_public_key = config.get('alipay_public_key')
        
        self.client = DefaultAlipayClient(alipay_client_config=alipay_config, logger=logger)
    
    def create_payment(self, request: PaymentRequest) -> PaymentResponse:
        """创建支付宝支付订单"""
        try:
            model = AlipayTradePagePayModel()
            model.out_trade_no = request.out_trade_no
            model.total_amount = request.total_amount
            model.subject = request.subject
            model.body = request.body or request.subject
            model.product_code = "FAST_INSTANT_TRADE_PAY"
            model.qr_pay_mode = '1'  # 二维码模式
            
            # 设置超时时间
            if request.timeout_express:
                model.timeout_express = request.timeout_express
            
            alipay_request = AlipayTradePagePayRequest(biz_model=model)

            # 设置回调地址
            if request.return_url:
                alipay_request.return_url = request.return_url
            if request.notify_url:
                alipay_request.notify_url = request.notify_url
            
            # 执行请求
            try:
                response_content = self.client.page_execute(alipay_request, http_method='GET')
            except Exception as e:
                logger.error(f"aaaFailed to create alipay payment: {str(e)}")
                raise

            logger.info(f"Alipay payment created for order {request.out_trade_no}")
            
            return PaymentResponse(
                success=True,
                payment_url=response_content,
                out_trade_no=request.out_trade_no,
                message="支付订单创建成功"
            )
            
        except Exception as e:
            logger.error(f"Failed to create alipay payment: {str(e)}")
            return PaymentResponse(
                success=False,
                out_trade_no=request.out_trade_no,
                message=f"创建支付订单失败: {str(e)}"
            )
    
    def query_payment(self, request: PaymentQueryRequest) -> PaymentQueryResponse:
        """查询支付宝支付状态"""
        try:
            model = AlipayTradeQueryModel()
            if request.out_trade_no:
                model.out_trade_no = request.out_trade_no
            if request.trade_no:
                model.trade_no = request.trade_no
            
            alipay_request = AlipayTradeQueryRequest(biz_model=model)
            response_content = self.client.execute(alipay_request)
            
            response = AlipayTradeQueryResponse()
            response.parse_response_content(response_content)
            
            if response.code == "10000":
                # 映射支付宝状态到标准状态
                status_mapping = {
                    "WAIT_BUYER_PAY": PaymentStatus.PENDING,
                    "TRADE_SUCCESS": PaymentStatus.SUCCESS,
                    "TRADE_FINISHED": PaymentStatus.SUCCESS,
                    "TRADE_CLOSED": PaymentStatus.CANCELLED
                }
                
                trade_status = status_mapping.get(response.trade_status, PaymentStatus.PENDING)
                
                return PaymentQueryResponse(
                    success=True,
                    trade_status=trade_status,
                    out_trade_no=response.out_trade_no,
                    trade_no=response.trade_no,
                    total_amount=float(response.total_amount) if response.total_amount else None,
                    buyer_id=response.buyer_user_id,
                    buyer_logon_id=response.buyer_logon_id,
                    send_pay_date=datetime.fromisoformat(response.send_pay_date.replace(' ', 'T')) if response.send_pay_date else None,
                    raw_response=json.loads(response.body) if response.body else None
                )
            else:
                return PaymentQueryResponse(
                    success=False,
                    trade_status=PaymentStatus.FAILED,
                    out_trade_no=request.out_trade_no or "",
                    message=response.msg
                )
                
        except Exception as e:
            logger.error(f"Failed to query alipay payment: {str(e)}")
            return PaymentQueryResponse(
                success=False,
                trade_status=PaymentStatus.FAILED,
                out_trade_no=request.out_trade_no or "",
                message=f"查询支付状态失败: {str(e)}"
            )

    def close_payment(self, out_trade_no: str, trade_no: Optional[str] = None) -> bool:
        """关闭支付宝支付订单"""
        try:
            model = AlipayTradeCloseModel()
            model.out_trade_no = out_trade_no
            if trade_no:
                model.trade_no = trade_no

            alipay_request = AlipayTradeCloseRequest(biz_model=model)
            response_content = self.client.execute(alipay_request)

            response = AlipayTradeCloseResponse()
            response.parse_response_content(response_content)

            if response.code == "10000":
                logger.info(f"Alipay payment closed for order {out_trade_no}")
                return True
            else:
                logger.warning(f"Failed to close alipay payment {out_trade_no}: {response.msg}")
                return False

        except Exception as e:
            logger.error(f"Failed to close alipay payment: {str(e)}")
            return False

    def refund_payment(self, request: RefundRequest) -> RefundResponse:
        """申请支付宝退款"""
        try:
            model = AlipayTradeRefundModel()
            if request.out_trade_no:
                model.out_trade_no = request.out_trade_no
            if request.trade_no:
                model.trade_no = request.trade_no

            model.refund_amount = str(request.refund_amount)
            model.refund_reason = request.refund_reason or "用户申请退款"

            if request.out_request_no:
                model.out_request_no = request.out_request_no

            alipay_request = AlipayTradeRefundRequest(biz_model=model)
            response_content = self.client.execute(alipay_request)

            response = AlipayTradeRefundResponse()
            response.parse_response_content(response_content)

            if response.code == "10000":
                return RefundResponse(
                    success=True,
                    out_trade_no=response.out_trade_no,
                    trade_no=response.trade_no,
                    refund_fee=float(response.refund_fee) if response.refund_fee else None,
                    gmt_refund_pay=datetime.fromisoformat(response.gmt_refund_pay.replace(' ', 'T')) if response.gmt_refund_pay else None,
                    fund_change=response.fund_change,
                    message="退款申请成功",
                    raw_response=json.loads(response.body) if response.body else None
                )
            else:
                return RefundResponse(
                    success=False,
                    out_trade_no=request.out_trade_no or "",
                    message=response.msg
                )

        except Exception as e:
            logger.error(f"Failed to refund alipay payment: {str(e)}")
            return RefundResponse(
                success=False,
                out_trade_no=request.out_trade_no or "",
                message=f"申请退款失败: {str(e)}"
            )

    def query_refund(self, request: RefundQueryRequest) -> RefundQueryResponse:
        """查询支付宝退款状态"""
        try:
            model = AlipayTradeFastpayRefundQueryModel()
            if request.out_trade_no:
                model.out_trade_no = request.out_trade_no
            if request.trade_no:
                model.trade_no = request.trade_no
            if request.out_request_no:
                model.out_request_no = request.out_request_no

            alipay_request = AlipayTradeFastpayRefundQueryRequest(biz_model=model)
            response_content = self.client.execute(alipay_request)

            response = AlipayTradeFastpayRefundQueryResponse()
            response.parse_response_content(response_content)

            if response.code == "10000":
                return RefundQueryResponse(
                    success=True,
                    out_trade_no=response.out_trade_no,
                    refund_status=response.refund_status if hasattr(response, 'refund_status') else None,
                    refund_amount=float(response.refund_amount) if hasattr(response, 'refund_amount') and response.refund_amount else None,
                    message="查询退款状态成功",
                    raw_response=json.loads(response.body) if response.body else None
                )
            else:
                return RefundQueryResponse(
                    success=False,
                    out_trade_no=request.out_trade_no or "",
                    message=response.msg
                )

        except Exception as e:
            logger.error(f"Failed to query alipay refund: {str(e)}")
            return RefundQueryResponse(
                success=False,
                out_trade_no=request.out_trade_no or "",
                message=f"查询退款状态失败: {str(e)}"
            )

    def verify_callback(self, callback_data: dict) -> bool:
        """验证支付宝回调签名"""
        try:
            # 这里需要实现支付宝的签名验证逻辑
            # 由于涉及复杂的签名算法，这里简化处理
            # 在实际生产环境中，需要严格验证签名

            # 检查必要字段
            required_fields = ['out_trade_no', 'trade_status', 'total_amount']
            for field in required_fields:
                if field not in callback_data:
                    logger.warning(f"Missing required field in callback: {field}")
                    return False

            logger.info(f"Alipay callback verified for order {callback_data.get('out_trade_no')}")
            return True

        except Exception as e:
            logger.error(f"Failed to verify alipay callback: {str(e)}")
            return False

    def parse_callback(self, callback_data: dict) -> PaymentQueryResponse:
        """解析支付宝回调数据"""
        try:
            # 映射支付宝状态
            status_mapping = {
                "WAIT_BUYER_PAY": PaymentStatus.PENDING,
                "TRADE_SUCCESS": PaymentStatus.SUCCESS,
                "TRADE_FINISHED": PaymentStatus.SUCCESS,
                "TRADE_CLOSED": PaymentStatus.CANCELLED
            }

            trade_status = status_mapping.get(
                callback_data.get('trade_status'),
                PaymentStatus.PENDING
            )

            return PaymentQueryResponse(
                success=True,
                trade_status=trade_status,
                out_trade_no=callback_data.get('out_trade_no', ''),
                trade_no=callback_data.get('trade_no'),
                total_amount=float(callback_data.get('total_amount', 0)),
                buyer_id=callback_data.get('buyer_id'),
                buyer_logon_id=callback_data.get('buyer_logon_id'),
                send_pay_date=datetime.fromisoformat(callback_data.get('gmt_payment', '').replace(' ', 'T')) if callback_data.get('gmt_payment') else None,
                raw_response=callback_data
            )

        except Exception as e:
            logger.error(f"Failed to parse alipay callback: {str(e)}")
            return PaymentQueryResponse(
                success=False,
                trade_status=PaymentStatus.FAILED,
                out_trade_no=callback_data.get('out_trade_no', ''),
                message=f"解析回调数据失败: {str(e)}"
            )

from sqlmodel import Session, select
from typing import List, Optional
import uuid

from models.product import Product
from models.order import Order, OrderItem
from models.payment import PaymentTransaction
from schemas.llm import LLMIntentRequest, ShoppingListItem
from services.payment import PaymentFactory, PaymentRequest, PaymentMethod
from utils.logger import get_logger


logger = get_logger(__name__)



class ShopService:
    def __init__(self, session: Session, payment_config: Optional[dict] = None):
        self.session = session
        self.payment_config = payment_config or {}
    
    def match_ingredients_to_products(self, ingredients: List[dict]) -> List[ShoppingListItem]:
        """Match recipe ingredients to available products"""
        try:
            shopping_list = []
            
            for ingredient in ingredients:
                ingredient_name = ingredient.get("name", "").lower()
                quantity = ingredient.get("quantity", 1)
                unit = ingredient.get("unit", "个")
                
                # Simple matching logic - in production, use more sophisticated matching
                statement = select(Product).where(Product.name.ilike(f"%{ingredient_name}%"))
                product = self.session.exec(statement).first()
                
                if product:
                    total_price = product.price * quantity
                    shopping_item = ShoppingListItem(
                        product_id=product.id,
                        product_name=product.name,
                        matched_ingredient=ingredient_name,
                        price_per_unit=product.price,
                        quantity=int(quantity),
                        total_price=total_price,
                        unit=unit,
                        image_url=product.image_url
                    )
                    shopping_list.append(shopping_item)
                else:
                    logger.warning(f"No product found for ingredient: {ingredient_name}")
            
            return shopping_list
        except Exception as e:
            logger.error(f"Error matching ingredients to products: {str(e)}")
            return []
    
    def create_order(self, device_id: str, shopping_list: List[ShoppingListItem]) -> Optional[Order]:
        """Create order from shopping list"""
        try:
            # 查找设备的内部ID
            from models.user_device_binding import Device
            statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(statement).first()

            if not device:
                logger.error(f"Device not found: {device_id}")
                return None

            # Calculate total amount
            total_amount = sum(item.total_price for item in shopping_list)

            # Create order
            order = Order(
                device_id=device.id,  # 使用设备的内部ID
                total_amount=total_amount,
                status="pending_payment"
            )
            
            self.session.add(order)
            self.session.flush()  # Get order ID
            
            # Create order items
            for item in shopping_list:
                order_item = OrderItem(
                    order_id=order.id,
                    product_id=item.product_id,
                    quantity=item.quantity,
                    price_at_purchase=item.price_per_unit
                )
                self.session.add(order_item)
            
            # Create payment transaction
            payment = PaymentTransaction(
                order_id=order.id,
                payment_method="qr_code",
                amount=total_amount,
                status="pending"
            )
            
            self.session.add(payment)
            self.session.commit()
            self.session.refresh(order)
            
            logger.info(f"Order {order.id} created for device {device_id}")
            return order
        except Exception as e:
            logger.error(f"Error creating order for device {device_id}: {str(e)}")
            self.session.rollback()
            return None
    
    def generate_payment_qr_code(self, order: Order, payment_method: PaymentMethod = PaymentMethod.ALIPAY) -> str:
        """Generate payment QR code URL using payment adapter"""
        try:
            # 获取支付配置
            method_config = self.payment_config.get(payment_method.value, {})
            if not method_config:
                logger.warning(f"No config found for payment method {payment_method}, using mock payment")
                return f"https://payment.example.com/pay?order_id={order.id}&amount={order.total_amount}"

            # 创建支付适配器
            payment_adapter = PaymentFactory.create_adapter(payment_method, method_config)

            # 创建支付请求
            payment_request = PaymentRequest(
                out_trade_no=order.id,
                total_amount=order.total_amount,
                subject=f"订单支付-{order.id}",
                body=f"机器人订单支付，订单号：{order.id}",
                notify_url=method_config.get('notify_url'),
                return_url=method_config.get('return_url')
            )

            # 创建支付
            payment_response = payment_adapter.create_payment(payment_request)

            if payment_response.success:
                # 更新支付交易记录
                self._update_payment_transaction(order.id, payment_response, payment_method)
                logger.info(f"Payment created successfully for order {order.id}")
                return payment_response.payment_url or payment_response.qr_code or ""
            else:
                logger.error(f"Failed to create payment for order {order.id}: {payment_response.message}")
                return f"https://payment.example.com/pay?order_id={order.id}&amount={order.total_amount}"

        except Exception as e:
            logger.error(f"Error generating payment for order {order.id}: {str(e)}")
            return f"https://payment.example.com/pay?order_id={order.id}&amount={order.total_amount}"

    def _update_payment_transaction(self, order_id: str, payment_response, payment_method: PaymentMethod):
        """更新支付交易记录"""
        try:
            # 查找现有的支付交易记录
            statement = select(PaymentTransaction).where(PaymentTransaction.order_id == order_id)
            payment_transaction = self.session.exec(statement).first()

            if payment_transaction:
                payment_transaction.payment_method = payment_method.value
                payment_transaction.transaction_id = payment_response.trade_no
                payment_transaction.status = "pending"
                if payment_response.raw_response:
                    payment_transaction.callback_data = str(payment_response.raw_response)

            self.session.add(payment_transaction)
            self.session.commit()

        except Exception as e:
            logger.error(f"Failed to update payment transaction: {str(e)}")
            self.session.rollback()
    
    def get_pending_orders(self) -> List[Order]:
        """Get all pending orders for staff"""
        try:
            statement = select(Order).where(Order.status.in_(["paid", "processing"]))
            orders = self.session.exec(statement).all()
            return list(orders)
        except Exception as e:
            logger.error(f"Error getting pending orders: {str(e)}")
            return []
    
    def update_order_status(self, order_id: str, status: str) -> bool:
        """Update order status"""
        try:
            statement = select(Order).where(Order.id == order_id)
            order = self.session.exec(statement).first()

            if not order:
                logger.warning(f"Order {order_id} not found")
                return False

            order.status = status
            self.session.add(order)
            self.session.commit()

            logger.info(f"Order {order_id} status updated to {status}")
            return True
        except Exception as e:
            logger.error(f"Error updating order {order_id} status: {str(e)}")
            self.session.rollback()
            return False

    def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """根据订单ID获取订单"""
        try:
            statement = select(Order).where(Order.id == order_id)
            order = self.session.exec(statement).first()
            return order
        except Exception as e:
            logger.error(f"Error getting order {order_id}: {str(e)}")
            return None

    def create_order_from_items(self, device_id: str, items: List[dict], total_amount: float) -> Optional[Order]:
        """根据商品列表创建订单"""
        try:
            # 创建订单
            order = Order(
                device_id=device_id,
                total_amount=total_amount,
                status="pending_payment"
            )

            self.session.add(order)
            self.session.flush()  # 获取订单ID

            # 创建订单项（如果需要的话）
            for item in items:
                order_item = OrderItem(
                    order_id=order.id,
                    product_id=item.get('product_id', ''),
                    quantity=item.get('quantity', 1),
                    price_at_purchase=item.get('price', 0.0)
                )
                self.session.add(order_item)

            # 创建支付交易记录
            payment = PaymentTransaction(
                order_id=order.id,
                payment_method="pending",  # 待选择支付方式
                amount=total_amount,
                status="pending"
            )

            self.session.add(payment)
            self.session.commit()
            self.session.refresh(order)

            logger.info(f"Order {order.id} created from items for device {device_id}")
            return order
        except Exception as e:
            logger.error(f"Error creating order from items for device {device_id}: {str(e)}")
            self.session.rollback()
            return None
